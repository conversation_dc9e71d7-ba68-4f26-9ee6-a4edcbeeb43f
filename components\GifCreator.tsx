
import React, { useState, useRef, useCallback } from 'react';
import { Button } from './ui/Button';
import { Spinner } from './ui/Spinner';
import { UploadIcon, DownloadIcon, XCircleIcon } from './ui/icons';

interface ImagePreview {
  file: File;
  previewUrl: string;
}

const GifCreator: React.FC = () => {
  const [images, setImages] = useState<ImagePreview[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [gifUrl, setGifUrl] = useState<string | null>(null);
  const [delay, setDelay] = useState<number>(200);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      setError(null);
      setGifUrl(null);
      const newImagePreviews: ImagePreview[] = [];
      Array.from(files).forEach(file => {
        if (file.type.startsWith('image/')) {
          newImagePreviews.push({ file, previewUrl: URL.createObjectURL(file) });
        }
      });
      setImages(prev => [...prev, ...newImagePreviews]);
    }
  };

  const removeImage = (indexToRemove: number) => {
    const imageUrlToRevoke = images[indexToRemove].previewUrl;
    URL.revokeObjectURL(imageUrlToRevoke);
    setImages(prev => prev.filter((_, index) => index !== indexToRemove));
  };
  
  const handleCreateGif = useCallback(async () => {
    if (images.length === 0) {
      setError('Please add at least one image.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setGifUrl(null);

    try {
      // Verificar que las librerías estén disponibles
      if (!window.gifLibrariesLoaded) {
        if (window.gifLibrariesError) {
          throw new Error(`Error de librerías: ${window.gifLibrariesError}`);
        }

        // Esperar a que las librerías se carguen
        setError('Esperando a que se carguen las librerías...');
        await new Promise((resolve, reject) => {
          if (window.gifLibrariesLoaded) {
            resolve(true);
            return;
          }

          const timeout = setTimeout(() => {
            reject(new Error('Timeout esperando librerías'));
          }, 10000);

          window.addEventListener('gifLibrariesReady', () => {
            clearTimeout(timeout);
            resolve(true);
          }, { once: true });
        });

        setError(null);
      }

      if (!window.GIF) {
        throw new Error('La librería de creación de GIF no está disponible. Por favor, recarga la página e intenta de nuevo.');
      }

      // gif.js worker script must be accessible. Using a CDN.
      const workerScript = 'https://cdnjs.cloudflare.com/ajax/libs/gif.js/0.2.0/gif.worker.js';

      const gif = new window.GIF({
        workers: 2,
        quality: 10,
        workerScript: workerScript,
      });

      const imageElements = await Promise.all(
          images.map(img => new Promise<HTMLImageElement>((resolve, reject) => {
              const imageEl = new Image();
              imageEl.onload = () => resolve(imageEl);
              imageEl.onerror = () => reject(new Error(`Failed to load image: ${img.file.name}`));
              imageEl.src = img.previewUrl;
          }))
      );

      imageElements.forEach(imgEl => {
          gif.addFrame(imgEl, { delay: delay });
      });

      gif.on('finished', (blob: Blob) => {
          const url = URL.createObjectURL(blob);
          setGifUrl(url);
          setIsLoading(false);
      });

      gif.on('abort', () => {
           setError("GIF creation was aborted.");
           setIsLoading(false);
      });

      gif.render();

    } catch(e) {
        setError(e instanceof Error ? e.message : "An error occurred while preparing images.");
        setIsLoading(false);
    }

  }, [images, delay]);

  return (
    <div>
      <h2 className="text-2xl font-bold text-center text-indigo-300 mb-6">Image to GIF Creator</h2>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept="image/png,image/jpeg,image/webp"
        multiple
        className="hidden"
      />
      
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-6">
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isLoading}
        >
          <UploadIcon />
          Add Images
        </Button>
        <div className="flex items-center gap-2">
            <label htmlFor="delay" className="text-gray-300 font-medium">Frame Delay (ms):</label>
            <input 
                id="delay"
                type="number"
                value={delay}
                onChange={(e) => setDelay(Math.max(20, parseInt(e.target.value, 10) || 200))}
                className="bg-gray-700 text-white w-24 rounded-md p-2 border border-gray-600 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                disabled={isLoading}
            />
        </div>
      </div>

      {images.length > 0 && (
        <div className="mb-6">
            <h3 className="text-xl font-semibold text-center mb-4">
                {images.length} Images Selected
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                {images.map((img, index) => (
                    <div key={index} className="group relative aspect-square bg-gray-700 rounded-lg overflow-hidden shadow-md">
                        <img src={img.previewUrl} alt={`Preview ${index + 1}`} className="w-full h-full object-cover" />
                        <button onClick={() => removeImage(index)} className="absolute top-1 right-1 text-white bg-black/50 rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white">
                            <XCircleIcon />
                        </button>
                    </div>
                ))}
            </div>
        </div>
      )}

      {!isLoading && images.length === 0 && (
         <div className="text-center text-gray-500 py-10 border-2 border-dashed border-gray-600 rounded-lg">
            <p className="text-lg">Add some images to get started.</p>
        </div>
      )}
      
      {error && <p className="text-center text-red-400 bg-red-900/50 p-3 rounded-md mb-4">{error}</p>}
      
      <div className="flex justify-center mt-6">
         <Button
            onClick={handleCreateGif}
            disabled={isLoading || images.length === 0}
            variant="primary"
            size="lg"
          >
            {isLoading ? 'Creating GIF...' : 'Create GIF'}
          </Button>
      </div>
      
      {isLoading && (
        <div className="flex flex-col items-center justify-center p-8">
          <Spinner />
          <p className="mt-4 text-lg text-gray-400">Your GIF is being generated...</p>
        </div>
      )}

      {gifUrl && (
        <div className="mt-8 text-center">
            <h3 className="text-xl font-semibold mb-4">Your GIF is Ready!</h3>
            <img src={gifUrl} alt="Generated GIF" className="max-w-md mx-auto rounded-lg shadow-lg border-4 border-gray-700" />
            <div className="mt-6">
                 <a href={gifUrl} download="created-by-toolkit.gif">
                    <Button variant="primary" size="lg">
                        <DownloadIcon />
                        Download GIF
                    </Button>
                 </a>
            </div>
        </div>
      )}
    </div>
  );
};

export default GifCreator;