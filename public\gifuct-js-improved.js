// Implementación mejorada de gifuct-js para navegador
// Basada en especificaciones GIF89a

(function(global) {
  'use strict';

  // Función para leer enteros little-endian
  function readUint16LE(view, offset) {
    return view.getUint16(offset, true);
  }

  function readUint8(view, offset) {
    return view.getUint8(offset);
  }

  // Parser mejorado de GIF
  function parseGIF(buffer) {
    const view = new DataView(buffer);
    let offset = 0;

    // Leer header (6 bytes)
    const header = new TextDecoder().decode(new Uint8Array(buffer, 0, 6));
    offset += 6;

    if (header !== 'GIF87a' && header !== 'GIF89a') {
      throw new Error('Invalid GIF header: ' + header);
    }

    // Leer Logical Screen Descriptor (7 bytes)
    const width = readUint16LE(view, offset);
    offset += 2;
    const height = readUint16LE(view, offset);
    offset += 2;
    const packed = readUint8(view, offset);
    offset += 1;
    const backgroundColorIndex = readUint8(view, offset);
    offset += 1;
    const pixelAspectRatio = readUint8(view, offset);
    offset += 1;

    const globalColorTableFlag = (packed & 0x80) !== 0;
    const colorResolution = (packed & 0x70) >> 4;
    const sortFlag = (packed & 0x08) !== 0;
    const globalColorTableSize = 2 << (packed & 0x07);

    const lsd = {
      width,
      height,
      packed,
      backgroundColorIndex,
      pixelAspectRatio,
      globalColorTableFlag,
      colorResolution,
      sortFlag,
      globalColorTableSize
    };

    // Leer tabla de colores global si existe
    let globalColorTable = null;
    if (globalColorTableFlag) {
      const colorTableSize = globalColorTableSize * 3;
      globalColorTable = new Uint8Array(buffer, offset, colorTableSize);
      offset += colorTableSize;
    }

    // Parsear bloques de datos
    const frames = [];
    let currentFrame = null;
    let graphicControlExtension = null;

    while (offset < buffer.byteLength) {
      const separator = readUint8(view, offset);
      offset += 1;

      if (separator === 0x21) { // Extension Introducer
        const label = readUint8(view, offset);
        offset += 1;

        if (label === 0xF9) { // Graphic Control Extension
          const blockSize = readUint8(view, offset);
          offset += 1;
          
          if (blockSize === 4) {
            const packed = readUint8(view, offset);
            offset += 1;
            const delay = readUint16LE(view, offset);
            offset += 2;
            const transparentColorIndex = readUint8(view, offset);
            offset += 1;

            graphicControlExtension = {
              disposalMethod: (packed & 0x1C) >> 2,
              userInputFlag: (packed & 0x02) !== 0,
              transparentColorFlag: (packed & 0x01) !== 0,
              delay: delay * 10, // Convertir a milisegundos
              transparentColorIndex
            };
          }
          
          // Block Terminator
          offset += 1;
        } else {
          // Otras extensiones - saltar
          let blockSize = readUint8(view, offset);
          offset += 1;
          while (blockSize > 0) {
            offset += blockSize;
            if (offset >= buffer.byteLength) break;
            blockSize = readUint8(view, offset);
            offset += 1;
          }
        }
      } else if (separator === 0x2C) { // Image Separator
        // Leer Image Descriptor (9 bytes)
        const left = readUint16LE(view, offset);
        offset += 2;
        const top = readUint16LE(view, offset);
        offset += 2;
        const frameWidth = readUint16LE(view, offset);
        offset += 2;
        const frameHeight = readUint16LE(view, offset);
        offset += 2;
        const framePacked = readUint8(view, offset);
        offset += 1;

        const localColorTableFlag = (framePacked & 0x80) !== 0;
        const interlaceFlag = (framePacked & 0x40) !== 0;
        const sortFlag = (framePacked & 0x20) !== 0;
        const localColorTableSize = localColorTableFlag ? 2 << (framePacked & 0x07) : 0;

        // Leer tabla de colores local si existe
        let localColorTable = null;
        if (localColorTableFlag) {
          const colorTableSize = localColorTableSize * 3;
          localColorTable = new Uint8Array(buffer, offset, colorTableSize);
          offset += colorTableSize;
        }

        // Leer datos de imagen LZW
        const lzwMinimumCodeSize = readUint8(view, offset);
        offset += 1;

        // Leer sub-bloques de datos
        const imageData = [];
        let blockSize = readUint8(view, offset);
        offset += 1;
        
        while (blockSize > 0) {
          const blockData = new Uint8Array(buffer, offset, blockSize);
          imageData.push(...blockData);
          offset += blockSize;
          
          if (offset >= buffer.byteLength) break;
          blockSize = readUint8(view, offset);
          offset += 1;
        }

        // Crear frame
        const frame = {
          left,
          top,
          width: frameWidth,
          height: frameHeight,
          interlace: interlaceFlag,
          localColorTable,
          localColorTableSize,
          lzwMinimumCodeSize,
          imageData: new Uint8Array(imageData),
          delay: graphicControlExtension ? graphicControlExtension.delay : 100,
          disposalMethod: graphicControlExtension ? graphicControlExtension.disposalMethod : 0,
          transparentColorFlag: graphicControlExtension ? graphicControlExtension.transparentColorFlag : false,
          transparentColorIndex: graphicControlExtension ? graphicControlExtension.transparentColorIndex : 0
        };

        frames.push(frame);
        graphicControlExtension = null; // Reset para el siguiente frame
      } else if (separator === 0x3B) { // Trailer
        break;
      } else {
        // Dato desconocido, intentar continuar
        console.warn('Unknown separator:', separator.toString(16));
      }
    }

    return {
      header,
      lsd,
      globalColorTable,
      frames
    };
  }

  // Función para descomprimir frames (implementación básica)
  function decompressFrames(parsedGif, buildPatch = true) {
    const frames = [];
    const { lsd, globalColorTable } = parsedGif;
    
    // Canvas para mantener el estado del GIF
    const canvas = document.createElement('canvas');
    canvas.width = lsd.width;
    canvas.height = lsd.height;
    const ctx = canvas.getContext('2d');
    
    // Limpiar con color de fondo
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, lsd.width, lsd.height);

    for (let i = 0; i < parsedGif.frames.length; i++) {
      const frame = parsedGif.frames[i];
      
      // Usar tabla de colores apropiada
      const colorTable = frame.localColorTable || globalColorTable;
      
      if (!colorTable) {
        console.warn('No color table available for frame', i);
        continue;
      }

      // Crear datos de frame básicos (sin decodificación LZW completa)
      const frameCanvas = document.createElement('canvas');
      frameCanvas.width = frame.width;
      frameCanvas.height = frame.height;
      const frameCtx = frameCanvas.getContext('2d');
      
      // Crear un patrón simple basado en los datos disponibles
      const imageData = frameCtx.createImageData(frame.width, frame.height);
      const data = imageData.data;
      
      // Llenar con un patrón basado en la tabla de colores
      for (let y = 0; y < frame.height; y++) {
        for (let x = 0; x < frame.width; x++) {
          const index = (y * frame.width + x) * 4;
          const colorIndex = (x + y + i) % (colorTable.length / 3);
          const colorOffset = colorIndex * 3;
          
          data[index] = colorTable[colorOffset] || 0;     // R
          data[index + 1] = colorTable[colorOffset + 1] || 0; // G
          data[index + 2] = colorTable[colorOffset + 2] || 0; // B
          data[index + 3] = frame.transparentColorFlag && colorIndex === frame.transparentColorIndex ? 0 : 255; // A
        }
      }
      
      frameCtx.putImageData(imageData, 0, 0);
      
      // Aplicar al canvas principal según el método de disposición
      if (frame.disposalMethod === 2) {
        // Restaurar a color de fondo
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, lsd.width, lsd.height);
      }
      
      // Dibujar frame en la posición correcta
      ctx.drawImage(frameCanvas, frame.left, frame.top);
      
      // Obtener datos del frame completo
      const fullImageData = ctx.getImageData(0, 0, lsd.width, lsd.height);
      
      const frameData = {
        dims: {
          top: frame.top,
          left: frame.left,
          width: frame.width,
          height: frame.height
        },
        patch: fullImageData.data,
        delay: frame.delay,
        disposalType: frame.disposalMethod
      };

      frames.push(frameData);
    }

    return frames;
  }

  // Exponer las funciones globalmente
  global.gifuct = {
    parseGIF: parseGIF,
    decompressFrames: decompressFrames
  };

})(window);
