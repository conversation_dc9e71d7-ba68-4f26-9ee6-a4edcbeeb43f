# GIF TOOLKIT - Script de PowerShell
Write-Host "========================================" -ForegroundColor Green
Write-Host "   GIF TOOLKIT - Iniciando aplicacion" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Verificar Node.js
Write-Host "Verificando Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js no esta instalado" -ForegroundColor Red
    Write-Host "Instala Node.js desde https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Presiona Enter para salir"
    exit 1
}

# Verificar npm
Write-Host "Verificando npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version 2>$null
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: npm no esta disponible" -ForegroundColor Red
    Read-Host "Presiona Enter para salir"
    exit 1
}

# Verificar dependencias
Write-Host ""
Write-Host "Verificando dependencias..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "Instalando dependencias por primera vez..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Fallo la instalacion de dependencias" -ForegroundColor Red
        Read-Host "Presiona Enter para salir"
        exit 1
    }
    Write-Host "Dependencias instaladas correctamente!" -ForegroundColor Green
} else {
    Write-Host "Dependencias ya instaladas." -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Iniciando servidor de desarrollo..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "La aplicacion estara disponible en:" -ForegroundColor Yellow
Write-Host "http://localhost:5173" -ForegroundColor Cyan
Write-Host ""
Write-Host "Abriendo navegador en 3 segundos..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Abrir navegador
Start-Process "http://localhost:5173"

Write-Host ""
Write-Host "Para detener el servidor presiona Ctrl+C" -ForegroundColor Yellow
Write-Host ""

# Ejecutar la aplicacion
npm run dev

Write-Host ""
Write-Host "El servidor se ha detenido." -ForegroundColor Yellow
Read-Host "Presiona Enter para salir"
