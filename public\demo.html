<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo GIF Toolkit</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #1a1a1a; 
            color: white; 
            max-width: 800px; 
            margin: 0 auto; 
        }
        .demo-section { 
            background: #2a2a2a; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
        }
        button { 
            background: #4f46e5; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { background: #3730a3; }
        .result { 
            margin-top: 20px; 
            padding: 15px; 
            background: #374151; 
            border-radius: 5px; 
        }
        .success { border-left: 4px solid #10b981; }
        .error { border-left: 4px solid #ef4444; }
        canvas { border: 2px solid #4b5563; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🎬 Demo GIF Toolkit</h1>
    <p>Prueba las funcionalidades de la aplicación con ejemplos simples.</p>

    <div class="demo-section">
        <h2>🎨 Crear GIF desde Canvas</h2>
        <p>Genera un GIF simple con formas animadas.</p>
        <button onclick="createDemoGif()">Crear GIF de Prueba</button>
        <div id="gif-result"></div>
    </div>

    <div class="demo-section">
        <h2>📁 Cargar GIF de Prueba</h2>
        <p>Carga un archivo GIF para extraer sus frames.</p>
        <input type="file" id="gif-input" accept="image/gif" onchange="loadGifFile(this)">
        <div id="extract-result"></div>
    </div>

    <div class="demo-section">
        <h2>🔧 Estado de las Librerías</h2>
        <button onclick="checkLibraryStatus()">Verificar Estado</button>
        <div id="library-status"></div>
    </div>

    <!-- Cargar librerías -->
    <script src="/gifuct-js-improved.js"></script>
    <script src="/gif-js-browser.js"></script>

    <script>
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="result ${isSuccess ? 'success' : 'error'}">
                    ${message}
                </div>
            `;
        }

        function createDemoGif() {
            try {
                showResult('gif-result', '⏳ Creando GIF de prueba...');

                // Crear frames de prueba
                const frames = [];
                const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff'];

                for (let i = 0; i < 5; i++) {
                    const canvas = document.createElement('canvas');
                    canvas.width = 200;
                    canvas.height = 200;
                    const ctx = canvas.getContext('2d');

                    // Fondo
                    ctx.fillStyle = '#000000';
                    ctx.fillRect(0, 0, 200, 200);

                    // Círculo animado
                    ctx.fillStyle = colors[i];
                    const x = 50 + (i * 25);
                    const y = 100;
                    ctx.beginPath();
                    ctx.arc(x, y, 30, 0, 2 * Math.PI);
                    ctx.fill();

                    // Texto
                    ctx.fillStyle = 'white';
                    ctx.font = '20px Arial';
                    ctx.fillText(`Frame ${i + 1}`, 10, 30);

                    frames.push(canvas);
                }

                // Crear GIF
                const gif = new GIF({
                    workers: 1,
                    quality: 10,
                    width: 200,
                    height: 200
                });

                frames.forEach(canvas => {
                    gif.addFrame(canvas, { delay: 500 });
                });

                gif.on('finished', function(blob) {
                    const url = URL.createObjectURL(blob);
                    showResult('gif-result', `
                        ✅ GIF creado exitosamente!<br>
                        <img src="${url}" style="max-width: 300px; border-radius: 5px;"><br>
                        <a href="${url}" download="demo.gif" style="color: #60a5fa;">📥 Descargar GIF</a>
                    `);
                });

                gif.on('abort', function() {
                    showResult('gif-result', '❌ Error al crear el GIF', false);
                });

                gif.render();

            } catch (error) {
                showResult('gif-result', `❌ Error: ${error.message}`, false);
            }
        }

        function loadGifFile(input) {
            const file = input.files[0];
            if (!file) return;

            showResult('extract-result', '⏳ Procesando GIF...');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const buffer = e.target.result;
                    const parsedGif = gifuct.parseGIF(buffer);
                    const frames = gifuct.decompressFrames(parsedGif, true);

                    let resultHtml = `
                        ✅ GIF procesado exitosamente!<br>
                        📊 Dimensiones: ${parsedGif.lsd.width}x${parsedGif.lsd.height}<br>
                        🎞️ Frames encontrados: ${frames.length}<br><br>
                    `;

                    // Mostrar algunos frames
                    frames.slice(0, 3).forEach((frame, index) => {
                        const canvas = document.createElement('canvas');
                        canvas.width = frame.dims.width;
                        canvas.height = frame.dims.height;
                        canvas.style.margin = '5px';
                        canvas.style.border = '1px solid #666';
                        
                        const ctx = canvas.getContext('2d');
                        const imageData = new ImageData(frame.patch, frame.dims.width, frame.dims.height);
                        ctx.putImageData(imageData, 0, 0);
                        
                        resultHtml += `Frame ${index + 1}: `;
                        document.getElementById('extract-result').innerHTML = resultHtml;
                        document.getElementById('extract-result').appendChild(canvas);
                    });

                } catch (error) {
                    showResult('extract-result', `❌ Error procesando GIF: ${error.message}`, false);
                }
            };

            reader.readAsArrayBuffer(file);
        }

        function checkLibraryStatus() {
            const gifuctAvailable = window.gifuct && window.gifuct.parseGIF;
            const gifJsAvailable = window.GIF;

            let status = '<h3>Estado de las Librerías:</h3>';
            status += `<p>🔧 gifuct-js: ${gifuctAvailable ? '✅ Disponible' : '❌ No disponible'}</p>`;
            status += `<p>🎨 gif.js: ${gifJsAvailable ? '✅ Disponible' : '❌ No disponible'}</p>`;
            
            if (gifuctAvailable && gifJsAvailable) {
                status += '<p>🎉 ¡Todas las librerías están funcionando!</p>';
            } else {
                status += '<p>⚠️ Algunas librerías no están disponibles</p>';
            }

            showResult('library-status', status);
        }

        // Verificar estado al cargar
        window.addEventListener('load', () => {
            setTimeout(checkLibraryStatus, 1000);
        });
    </script>
</body>
</html>
