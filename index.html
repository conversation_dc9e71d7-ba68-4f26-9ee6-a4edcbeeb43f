
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GIF Toolkit</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Cargar librerías GIF con verificación -->
    <script>
      // Estado de carga de librerías
      window.gifLibrariesLoaded = false;
      window.gifLibrariesError = null;

      // Función para verificar si las librerías están cargadas
      function checkLibraries() {
        const gifuctLoaded = window.gifuct && window.gifuct.parseGIF && window.gifuct.decompressFrames;
        const gifJsLoaded = window.GIF;

        console.log('🔍 Verificando librerías:', {
          gifuct: !!window.gifuct,
          gifuctParseGIF: !!(window.gifuct && window.gifuct.parseGIF),
          gifuctDecompressFrames: !!(window.gifuct && window.gifuct.decompressFrames),
          GIF: !!window.GIF,
          gifuctLoaded,
          gifJsLoaded
        });

        if (gifuctLoaded && gifJsLoaded) {
          window.gifLibrariesLoaded = true;
          console.log('🎉 Todas las librerías GIF cargadas correctamente (versión offline)');

          // Mostrar mensaje informativo
          const infoDiv = document.createElement('div');
          infoDiv.id = 'offline-info';
          infoDiv.innerHTML = `
            <div style="background: #059669; color: white; padding: 0.75rem; margin: 1rem; border-radius: 0.5rem; text-align: center; font-size: 0.9rem;">
              ✅ Modo offline activado - Las librerías GIF se cargaron localmente
              <button onclick="document.getElementById('offline-info').remove()" style="background: rgba(255,255,255,0.2); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; border: none; margin-left: 1rem; cursor: pointer;">
                ✕
              </button>
            </div>
          `;

          // Insertar al principio del body cuando esté disponible
          setTimeout(() => {
            if (document.body && !document.getElementById('offline-info')) {
              document.body.insertBefore(infoDiv, document.body.firstChild);
              // Auto-remover después de 5 segundos
              setTimeout(() => {
                const info = document.getElementById('offline-info');
                if (info) info.remove();
              }, 5000);
            }
          }, 1000);

          // Disparar evento personalizado
          window.dispatchEvent(new CustomEvent('gifLibrariesReady'));
        }

        return gifuctLoaded && gifJsLoaded;
      }

      // Función para mostrar error
      function showLibraryError(message) {
        window.gifLibrariesError = message;
        console.error('💥 Error cargando librerías GIF:', message);

        // Mostrar mensaje de error al usuario
        const errorDiv = document.createElement('div');
        errorDiv.id = 'gif-library-error';
        errorDiv.innerHTML = `
          <div style="background: #dc2626; color: white; padding: 1rem; margin: 1rem; border-radius: 0.5rem; text-align: center;">
            <h3>❌ Error cargando librerías</h3>
            <p>${message}</p>
            <button onclick="location.reload()" style="background: #fff; color: #dc2626; padding: 0.5rem 1rem; border-radius: 0.25rem; border: none; margin-top: 0.5rem; cursor: pointer;">
              🔄 Recargar página
            </button>
          </div>
        `;

        // Insertar al principio del body cuando esté disponible
        if (document.body) {
          document.body.insertBefore(errorDiv, document.body.firstChild);
        } else {
          document.addEventListener('DOMContentLoaded', () => {
            document.body.insertBefore(errorDiv, document.body.firstChild);
          });
        }
      }

      // Timeout para verificar carga
      setTimeout(() => {
        if (!checkLibraries()) {
          showLibraryError('Las librerías de procesamiento de GIF no se cargaron correctamente. Usando versión offline simplificada.');
        }
      }, 3000);
    </script>

    <!-- Cargar librerías GIF locales mejoradas -->
    <script src="/gifuct-js-improved.js"
            onload="console.log('✅ gifuct-js mejorado cargado'); checkLibraries()"
            onerror="showLibraryError('Error cargando gifuct-js mejorado')"></script>
    <script src="/gif-js-browser.js"
            onload="console.log('✅ gif.js local cargado'); checkLibraries()"
            onerror="showLibraryError('Error cargando gif.js local')"></script>
    <style>
      body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-900 text-gray-100">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
