// Versión simplificada de gif.js para navegador
// Implementación básica para crear GIFs

(function(global) {
  'use strict';

  function GIF(options) {
    this.options = Object.assign({
      workers: 2,
      quality: 10,
      width: null,
      height: null,
      transparent: null,
      background: '#fff',
      ratio: 1,
      dispose: -1,
      repeat: 0,
      workerScript: null
    }, options || {});

    this.frames = [];
    this.freeWorkers = [];
    this.activeWorkers = [];
    this.running = false;
    this.events = {};
  }

  GIF.prototype.on = function(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  };

  GIF.prototype.emit = function(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  };

  GIF.prototype.addFrame = function(element, options) {
    options = options || {};
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Determinar dimensiones
    let width, height;
    if (element instanceof HTMLImageElement) {
      width = element.naturalWidth || element.width;
      height = element.naturalHeight || element.height;
    } else if (element instanceof HTMLCanvasElement) {
      width = element.width;
      height = element.height;
    } else {
      throw new Error('Unsupported element type');
    }

    // Establecer dimensiones del GIF si no están definidas
    if (!this.options.width) this.options.width = width;
    if (!this.options.height) this.options.height = height;

    canvas.width = this.options.width;
    canvas.height = this.options.height;

    // Dibujar el elemento en el canvas
    ctx.drawImage(element, 0, 0, this.options.width, this.options.height);

    // Obtener datos de imagen
    const imageData = ctx.getImageData(0, 0, this.options.width, this.options.height);

    this.frames.push({
      data: imageData.data,
      width: this.options.width,
      height: this.options.height,
      delay: options.delay || 500
    });
  };

  GIF.prototype.render = function() {
    if (this.running) return;
    this.running = true;

    // Simulación de procesamiento con progreso
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += 0.1;
      this.emit('progress', progress);

      if (progress >= 1) {
        clearInterval(progressInterval);

        setTimeout(() => {
          try {
            const blob = this.createGifBlob();
            this.emit('finished', blob);
          } catch (error) {
            console.error('Error creating GIF:', error);
            this.emit('abort');
          }
          this.running = false;
        }, 500);
      }
    }, 100);
  };

  GIF.prototype.createGifBlob = function() {
    // Esta es una implementación muy simplificada
    // En una implementación real, necesitarías codificar el formato GIF completo
    
    const width = this.options.width;
    const height = this.options.height;
    
    // Crear un canvas para combinar todos los frames
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');

    // Por simplicidad, solo usamos el primer frame
    if (this.frames.length > 0) {
      const frame = this.frames[0];
      const imageData = new ImageData(frame.data, frame.width, frame.height);
      ctx.putImageData(imageData, 0, 0);
    }

    // Convertir a blob (será un PNG, no un GIF real)
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      }, 'image/png');
    }).then(blob => blob);
  };

  // Crear un GIF básico (simplificado)
  GIF.prototype.createGifBlob = function() {
    const width = this.options.width;
    const height = this.options.height;

    if (this.frames.length === 0) {
      throw new Error('No frames to render');
    }

    // Para esta implementación simplificada, vamos a crear un canvas
    // que muestre todos los frames en una cuadrícula
    const cols = Math.ceil(Math.sqrt(this.frames.length));
    const rows = Math.ceil(this.frames.length / cols);

    const canvas = document.createElement('canvas');
    canvas.width = width * cols;
    canvas.height = height * rows;
    const ctx = canvas.getContext('2d');

    // Dibujar todos los frames en una cuadrícula
    this.frames.forEach((frame, index) => {
      const col = index % cols;
      const row = Math.floor(index / cols);
      const x = col * width;
      const y = row * height;

      const imageData = new ImageData(frame.data, frame.width, frame.height);

      // Crear un canvas temporal para este frame
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = frame.width;
      tempCanvas.height = frame.height;
      const tempCtx = tempCanvas.getContext('2d');
      tempCtx.putImageData(imageData, 0, 0);

      // Dibujar en el canvas principal
      ctx.drawImage(tempCanvas, x, y, width, height);
    });

    // Agregar texto indicando que es una versión simplificada
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, canvas.width, 30);
    ctx.fillStyle = 'white';
    ctx.font = '16px Arial';
    ctx.fillText(`GIF Simplificado - ${this.frames.length} frames`, 10, 20);

    // Convertir a blob
    const dataURL = canvas.toDataURL('image/png');
    const byteString = atob(dataURL.split(',')[1]);
    const mimeString = 'image/png'; // Será PNG, no GIF real

    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    return new Blob([ab], { type: mimeString });
  };

  GIF.prototype.abort = function() {
    this.running = false;
    this.emit('abort');
  };

  // Exponer la clase globalmente
  global.GIF = GIF;

})(window);
