// Versión simplificada de gifuct-js para navegador
// Basada en la librería gifuct-js pero adaptada para funcionar sin módulos

(function(global) {
  'use strict';

  // Esquema básico para parsear GIF
  const gifSchema = [
    {
      label: 'header',
      type: 'string',
      length: 6
    },
    {
      label: 'lsd',
      type: 'object',
      fields: [
        { label: 'width', type: 'uint16' },
        { label: 'height', type: 'uint16' },
        { label: 'packed', type: 'uint8' },
        { label: 'backgroundColorIndex', type: 'uint8' },
        { label: 'pixelAspectRatio', type: 'uint8' }
      ]
    }
  ];

  // Parser básico de GIF
  function parseGIF(buffer) {
    const view = new DataView(buffer);
    let offset = 0;

    // Leer header
    const header = new TextDecoder().decode(new Uint8Array(buffer, 0, 6));
    offset += 6;

    if (header !== 'GIF87a' && header !== 'GIF89a') {
      throw new Error('Invalid GIF header');
    }

    // Leer Logical Screen Descriptor
    const width = view.getUint16(offset, true);
    offset += 2;
    const height = view.getUint16(offset, true);
    offset += 2;
    const packed = view.getUint8(offset);
    offset += 1;
    const backgroundColorIndex = view.getUint8(offset);
    offset += 1;
    const pixelAspectRatio = view.getUint8(offset);
    offset += 1;

    const globalColorTableFlag = (packed & 0x80) !== 0;
    const colorResolution = (packed & 0x70) >> 4;
    const sortFlag = (packed & 0x08) !== 0;
    const globalColorTableSize = 2 << (packed & 0x07);

    const lsd = {
      width,
      height,
      packed,
      backgroundColorIndex,
      pixelAspectRatio,
      globalColorTableFlag,
      colorResolution,
      sortFlag,
      globalColorTableSize
    };

    // Leer tabla de colores global si existe
    let globalColorTable = null;
    if (globalColorTableFlag) {
      globalColorTable = new Uint8Array(buffer, offset, globalColorTableSize * 3);
      offset += globalColorTableSize * 3;
    }

    // Parsear frames (simplificado)
    const frames = [];
    
    while (offset < buffer.byteLength) {
      const separator = view.getUint8(offset);
      offset += 1;

      if (separator === 0x21) { // Extension
        const label = view.getUint8(offset);
        offset += 1;
        
        // Saltar datos de extensión
        let blockSize = view.getUint8(offset);
        offset += 1;
        while (blockSize > 0) {
          offset += blockSize;
          blockSize = view.getUint8(offset);
          offset += 1;
        }
      } else if (separator === 0x2C) { // Image
        // Leer Image Descriptor
        const left = view.getUint16(offset, true);
        offset += 2;
        const top = view.getUint16(offset, true);
        offset += 2;
        const frameWidth = view.getUint16(offset, true);
        offset += 2;
        const frameHeight = view.getUint16(offset, true);
        offset += 2;
        const framePacked = view.getUint8(offset);
        offset += 1;

        const localColorTableFlag = (framePacked & 0x80) !== 0;
        const interlaceFlag = (framePacked & 0x40) !== 0;
        const sortFlag = (framePacked & 0x20) !== 0;
        const localColorTableSize = localColorTableFlag ? 2 << (framePacked & 0x07) : 0;

        // Saltar tabla de colores local si existe
        if (localColorTableFlag) {
          offset += localColorTableSize * 3;
        }

        // Leer datos de imagen LZW
        const lzwMinimumCodeSize = view.getUint8(offset);
        offset += 1;

        // Saltar datos de imagen
        let blockSize = view.getUint8(offset);
        offset += 1;
        while (blockSize > 0) {
          offset += blockSize;
          blockSize = view.getUint8(offset);
          offset += 1;
        }

        frames.push({
          left,
          top,
          width: frameWidth,
          height: frameHeight,
          interlace: interlaceFlag,
          localColorTable: localColorTableFlag,
          localColorTableSize
        });
      } else if (separator === 0x3B) { // Trailer
        break;
      } else {
        // Dato desconocido, saltar
        offset += 1;
      }
    }

    return {
      header,
      lsd,
      globalColorTable,
      frames
    };
  }

  // Función simplificada para descomprimir frames
  function decompressFrames(parsedGif, buildPatch = true) {
    // Esta es una implementación muy básica
    // En una implementación real, necesitarías decodificar LZW
    const frames = [];
    
    for (let i = 0; i < parsedGif.frames.length; i++) {
      const frame = parsedGif.frames[i];
      
      // Crear datos de frame básicos
      const frameData = {
        dims: {
          top: frame.top,
          left: frame.left,
          width: frame.width,
          height: frame.height
        },
        patch: new Uint8ClampedArray(frame.width * frame.height * 4), // RGBA
        delay: 100, // Delay por defecto
        disposalType: 0
      };

      // Llenar con datos básicos (transparente)
      for (let j = 0; j < frameData.patch.length; j += 4) {
        frameData.patch[j] = 0;     // R
        frameData.patch[j + 1] = 0; // G
        frameData.patch[j + 2] = 0; // B
        frameData.patch[j + 3] = 0; // A (transparente)
      }

      frames.push(frameData);
    }

    return frames;
  }

  // Exponer las funciones globalmente
  global.gifuct = {
    parseGIF: parseGIF,
    decompressFrames: decompressFrames
  };

})(window);
