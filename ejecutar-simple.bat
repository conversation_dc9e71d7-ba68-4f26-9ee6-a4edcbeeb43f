@echo off
title GIF TOOLKIT - Modo Offline
color 0A
echo ========================================
echo    GIF TOOLKIT - Iniciando aplicacion
echo ========================================
echo.
echo [INFO] Modo offline activado - No requiere internet
echo [INFO] Librerias GIF incluidas localmente
echo.
echo Verificando dependencias...

if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
)

echo.
echo ========================================
echo Iniciando servidor...
echo ========================================
echo.
echo La aplicacion estara disponible en:
echo http://localhost:5173
echo.
echo Paginas disponibles:
echo - Aplicacion principal: http://localhost:5173
echo - Test de librerias:    http://localhost:5173/test-libraries.html
echo - Demo interactivo:     http://localhost:5173/demo.html
echo.
echo Para detener el servidor presiona Ctrl+C
echo.

start "" "http://localhost:5173"
npm run dev

pause
