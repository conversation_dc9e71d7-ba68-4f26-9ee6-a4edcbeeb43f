# GIF Toolkit

Una aplicación web para crear y extraer frames de archivos GIF.

## 🚀 Ejecución Rápida

**Opción 1: Usar archivo .bat (Windows)**
- Haz doble clic en `ejecutar-simple.bat`

**Opción 2: Usar PowerShell**
- Ejecuta: `.\ejecutar.ps1`

**Opción 3: Comandos manuales**
1. Instalar dependencias: `npm install`
2. Ejecutar servidor: `npm run dev`
3. Abrir: http://localhost:5173

## 🔧 Solución de Problemas

### Error: "No se pueden leer las propiedades de undefined (leyendo 'parseGIF')"

Este error indica que las librerías de procesamiento de GIF no se cargaron correctamente.

**Soluciones:**

1. **Verificar conexión a internet** - Las librerías se cargan desde CDN
2. **Recargar la página** - A veces las librerías tardan en cargar
3. **Verificar en consola del navegador** - Buscar errores de red
4. **Probar página de test** - Visita http://localhost:5173/test-libraries.html

### Otros problemas comunes

- **Puerto ocupado**: Si el puerto 5173 está ocupado, Vite usará automáticamente otro puerto
- **Node.js no encontrado**: Instala Node.js desde https://nodejs.org/
- **Dependencias faltantes**: Ejecuta `npm install`

## 📁 Estructura del Proyecto

```
gif-toolkit/
├── components/          # Componentes React
│   ├── GifCreator.tsx   # Crear GIFs desde imágenes
│   ├── GifExtractor.tsx # Extraer frames de GIFs
│   └── ui/              # Componentes de interfaz
├── ejecutar-simple.bat  # Script de ejecución (Windows)
├── ejecutar.ps1         # Script de PowerShell
├── test-libraries.html  # Página de prueba de librerías
└── public/              # Archivos estáticos

## 🛠️ Tecnologías

- **React 19** - Framework de interfaz
- **Vite** - Herramienta de desarrollo
- **TypeScript** - Tipado estático
- **Tailwind CSS** - Estilos
- **gifuct-js** - Procesamiento de GIFs
- **gif.js** - Creación de GIFs

## 📝 Uso

### Crear GIF desde imágenes
1. Ve a la pestaña "Images to GIF"
2. Selecciona múltiples imágenes
3. Ajusta el delay entre frames
4. Haz clic en "Create GIF"

### Extraer frames de GIF
1. Ve a la pestaña "GIF to Frames"
2. Selecciona un archivo GIF
3. Los frames se extraerán automáticamente
4. Descarga los frames individualmente

## 🔒 Privacidad

Todo el procesamiento se realiza localmente en tu navegador. No se suben archivos a ningún servidor.) to your Gemini API key
3. Run the app:
   `npm run dev`
