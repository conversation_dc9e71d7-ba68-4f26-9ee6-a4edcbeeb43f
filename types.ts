// This file contains declarations for global libraries loaded via script tags in index.html

// Since this file is a module, declarations for global variables must be
// placed inside a `declare global` block to augment the global scope.
declare global {
  /**
   * gifuct-js library for parsing GIFs.
   * This is attached to the global window object.
   */
  interface Window {
    gifuct: {
      parseGIF: (buffer: ArrayBuffer) => any;
      decompressFrames: (parsedGif: any, buildPatch: boolean) => FrameData[];
    };
    /**
     * gif.js library for creating GIFs.
     * This is attached to the global window object.
     */
    GIF: new (options: {
      workers: number;
      quality: number;
      workerScript: string;
    }) => any;
  }

  /**
   * Interface for a single frame extracted from a GIF by gifuct-js.
   * This is now a global interface and does not need to be exported.
   */
  interface FrameData {
    dims: {
      top: number;
      left: number;
      width: number;
      height: number;
    };
    patch: Uint8ClampedArray;
    delay: number;
    disposalType: number;
  }
}

// An empty export is required to make this file a module.
export {};
