<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GIF Libraries</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #22c55e; }
        .error { background: #ef4444; }
        .warning { background: #f59e0b; }
        .info { background: #3b82f6; }
    </style>
</head>
<body>
    <h1>🧪 Test de Librerías GIF</h1>
    <div id="status"></div>
    
    <script src="/gifuct-js-improved.js"></script>
    <script src="/gif-js-browser.js"></script>
    
    <script>
        const statusDiv = document.getElementById('status');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            statusDiv.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function testLibraries() {
            addStatus('🔍 Iniciando test de librerías...', 'info');
            
            // Test gifuct-js
            if (window.gifuct) {
                addStatus('✅ gifuct-js está disponible', 'success');
                
                if (window.gifuct.parseGIF) {
                    addStatus('✅ gifuct.parseGIF está disponible', 'success');
                } else {
                    addStatus('❌ gifuct.parseGIF NO está disponible', 'error');
                }
                
                if (window.gifuct.decompressFrames) {
                    addStatus('✅ gifuct.decompressFrames está disponible', 'success');
                } else {
                    addStatus('❌ gifuct.decompressFrames NO está disponible', 'error');
                }
            } else {
                addStatus('❌ gifuct-js NO está disponible', 'error');
            }
            
            // Test gif.js
            if (window.GIF) {
                addStatus('✅ gif.js está disponible', 'success');
                
                try {
                    const testGif = new window.GIF({
                        workers: 1,
                        quality: 10,
                        workerScript: 'https://cdnjs.cloudflare.com/ajax/libs/gif.js/0.2.0/gif.worker.js'
                    });
                    addStatus('✅ Se puede crear instancia de GIF', 'success');
                } catch (e) {
                    addStatus(`❌ Error creando instancia de GIF: ${e.message}`, 'error');
                }
            } else {
                addStatus('❌ gif.js NO está disponible', 'error');
            }
            
            // Resultado final
            const allLoaded = window.gifuct && window.gifuct.parseGIF && window.gifuct.decompressFrames && window.GIF;
            if (allLoaded) {
                addStatus('🎉 ¡Todas las librerías están funcionando correctamente!', 'success');
            } else {
                addStatus('💥 Hay problemas con las librerías', 'error');
            }
        }
        
        // Esperar un poco para que las librerías se carguen
        setTimeout(testLibraries, 1000);
    </script>
</body>
</html>
