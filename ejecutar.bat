@echo off
title GIF TOOLKIT - Servidor de Desarrollo
color 0A
echo.
echo ========================================
echo    GIF TOOLKIT - Iniciando aplicacion
echo ========================================
echo.

REM Cambiar al directorio del script
cd /d "%~dp0"

echo Verificando Node.js...
node --version 2>nul
if errorlevel 1 (
    echo.
    echo [ERROR] Node.js no esta instalado o no esta en el PATH
    echo Por favor instala Node.js desde https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Verificando npm...
npm --version 2>nul
if errorlevel 1 (
    echo.
    echo [ERROR] npm no esta disponible
    echo.
    pause
    exit /b 1
)

echo.
echo Verificando dependencias...
if not exist "node_modules" (
    echo Instalando dependencias por primera vez...
    echo Esto puede tomar unos minutos...
    echo.
    call npm install
    if errorlevel 1 (
        echo.
        echo [ERROR] Fallo la instalacion de dependencias
        echo.
        pause
        exit /b 1
    )
    echo.
    echo [OK] Dependencias instaladas correctamente!
) else (
    echo [OK] Dependencias ya instaladas.
)

echo.
echo ========================================
echo  INICIANDO SERVIDOR DE DESARROLLO...
echo ========================================
echo.
echo La aplicacion estara disponible en:
echo http://localhost:5173
echo.
echo NOTA:
echo - El navegador se abrira automaticamente en 5 segundos
echo - Para detener el servidor presiona Ctrl+C
echo.

REM Esperar 5 segundos y abrir navegador
echo Abriendo navegador en 5 segundos...
timeout /t 5 /nobreak >nul
start "" "http://localhost:5173"

echo.
echo [INICIANDO VITE...]
echo.

REM Ejecutar la aplicacion en modo desarrollo
call npm run dev

REM Pausar al final para ver cualquier mensaje
echo.
echo El servidor se ha detenido.
pause
