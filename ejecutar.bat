@echo off
echo ========================================
echo    GIF TOOLKIT - Iniciando aplicacion
echo ========================================
echo.

REM Verificar si Node.js esta instalado
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js no esta instalado o no esta en el PATH
    echo Por favor instala Node.js desde https://nodejs.org/
    pause
    exit /b 1
)

REM Verificar si npm esta instalado
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm no esta disponible
    pause
    exit /b 1
)

echo Verificando dependencias...

REM Verificar si existe node_modules
if not exist "node_modules" (
    echo Instalando dependencias por primera vez...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Fallo la instalacion de dependencias
        pause
        exit /b 1
    )
) else (
    echo Dependencias ya instaladas.
)

echo.
echo Iniciando servidor de desarrollo...
echo La aplicacion se abrira en http://localhost:5173
echo.
echo Presiona Ctrl+C para detener el servidor
echo ========================================

REM Ejecutar la aplicacion en modo desarrollo
npm run dev

pause
