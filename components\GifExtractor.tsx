import React, { useState, useRef, useCallback } from 'react';
import { Button } from './ui/Button';
import { Spinner } from './ui/Spinner';
import { UploadIcon, DownloadIcon } from './ui/icons';

const GifExtractor: React.FC = () => {
  const [frames, setFrames] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [gifName, setGifName] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processGif = useCallback(async (file: File) => {
    setIsLoading(true);
    setError(null);
    setFrames([]);
    setGifName(file.name.replace(/\.[^/.]+$/, ''));

    try {
      const buffer = await file.arrayBuffer();
      const parsedGif = window.gifuct.parseGIF(buffer);
      const decompressedFrames = window.gifuct.decompressFrames(parsedGif, true);

      if (!decompressedFrames || decompressedFrames.length === 0) {
        throw new Error('Could not extract frames from this GIF.');
      }
      
      const frameDataUrls: string[] = [];
      let fullCanvas = document.createElement('canvas');
      fullCanvas.width = parsedGif.lsd.width;
      fullCanvas.height = parsedGif.lsd.height;
      let fullCtx = fullCanvas.getContext('2d');
      if (!fullCtx) throw new Error("Could not create canvas context.");

      for (const frame of decompressedFrames) {
        const frameCanvas = document.createElement('canvas');
        frameCanvas.width = parsedGif.lsd.width;
        frameCanvas.height = parsedGif.lsd.height;
        const frameCtx = frameCanvas.getContext('2d');
        if (!frameCtx) continue;
        
        const frameImageData = frameCtx.createImageData(frame.dims.width, frame.dims.height);
        frameImageData.data.set(frame.patch);
        
        // Disposal method 2: restore to background color. We assume transparent.
        if (frame.disposalType === 2 && fullCtx) {
            fullCtx.clearRect(0, 0, fullCanvas.width, fullCanvas.height);
        }

        fullCtx.putImageData(frameImageData, frame.dims.left, frame.dims.top);

        frameDataUrls.push(fullCanvas.toDataURL('image/png'));
      }
      
      setFrames(frameDataUrls);
    } catch (e) {
      setError(e instanceof Error ? e.message : 'An unknown error occurred during GIF processing.');
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'image/gif') {
      processGif(file);
    } else {
      setError('Please select a valid GIF file.');
    }
  };

  const handleDownload = (frameUrl: string, index: number) => {
    const link = document.createElement('a');
    link.href = frameUrl;
    link.download = `${gifName}_frame_${String(index + 1).padStart(3, '0')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-center text-indigo-300 mb-6">GIF Frame Extractor</h2>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept="image/gif"
        className="hidden"
      />
      <div className="flex justify-center mb-6">
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isLoading}
          variant="primary"
        >
          <UploadIcon />
          {isLoading ? 'Processing GIF...' : 'Select GIF File'}
        </Button>
      </div>

      {isLoading && (
        <div className="flex flex-col items-center justify-center p-8">
          <Spinner />
          <p className="mt-4 text-lg text-gray-400">Extracting frames, please wait...</p>
        </div>
      )}

      {error && <p className="text-center text-red-400 bg-red-900/50 p-3 rounded-md">{error}</p>}
      
      {frames.length > 0 && (
        <div>
          <h3 className="text-xl font-semibold text-center mb-4">
            Extracted {frames.length} Frames
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {frames.map((frameUrl, index) => (
              <div key={index} className="group relative aspect-square bg-gray-700 rounded-lg overflow-hidden shadow-md">
                <img src={frameUrl} alt={`Frame ${index + 1}`} className="w-full h-full object-contain" />
                <div className="absolute inset-0 bg-black/70 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-2">
                  <p className="text-white font-bold text-sm mb-2">Frame {index + 1}</p>
                  <Button onClick={() => handleDownload(frameUrl, index)} size="sm">
                    <DownloadIcon />
                    PNG
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!isLoading && frames.length === 0 && !error && (
          <div className="text-center text-gray-500 py-10 border-2 border-dashed border-gray-600 rounded-lg">
              <p className="text-lg">Upload a GIF to see its frames here.</p>
          </div>
      )}
    </div>
  );
};

export default GifExtractor;