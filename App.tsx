
import React, { useState } from 'react';
import GifExtractor from './components/GifExtractor';
import GifCreator from './components/GifCreator';
import { FilmIcon, PlusCircleIcon } from './components/ui/icons';

type Tab = 'extractor' | 'creator';

const App: React.FC = () => {
  const [activeTab, setActiveTab] = useState<Tab>('extractor');

  const TabButton = ({ tab, label, icon }: { tab: Tab; label: string; icon: React.ReactNode }) => (
    <button
      onClick={() => setActiveTab(tab)}
      className={`flex items-center justify-center w-full px-4 py-3 font-semibold rounded-lg transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900 focus-visible:ring-indigo-500 ${
        activeTab === tab
          ? 'bg-indigo-600 text-white shadow-lg'
          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
      }`}
    >
      {icon}
      <span className="ml-2">{label}</span>
    </button>
  );

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 flex flex-col items-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-5xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
            GIF Toolkit
          </h1>
          <p className="mt-2 text-lg text-gray-400">
            Extract frames from GIFs or create your own animated GIFs from images.
          </p>
        </header>

        <main>
          <div className="grid grid-cols-2 gap-4 mb-8 max-w-md mx-auto">
            <TabButton tab="extractor" label="GIF to Frames" icon={<FilmIcon />} />
            <TabButton tab="creator" label="Images to GIF" icon={<PlusCircleIcon />} />
          </div>

          <div className="bg-gray-800 rounded-xl shadow-2xl p-6 md:p-8">
            {activeTab === 'extractor' && <GifExtractor />}
            {activeTab === 'creator' && <GifCreator />}
          </div>
        </main>
        
        <footer className="text-center mt-8 text-gray-500 text-sm">
            <p>All processing is done directly in your browser. No files are uploaded to any server.</p>
        </footer>
      </div>
    </div>
  );
};

export default App;
